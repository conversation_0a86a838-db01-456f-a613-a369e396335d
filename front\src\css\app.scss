// app global css in SCSS form
html {
  background-color: #f5f5f5;
}

* {
  touch-action: manipulation;
  -webkit-touch-callout: none;
}

.q-field {
  &.q-field--readonly {
    .q-field__control {
      &:before {
        border-style: solid;
      }
    }
  }
}

.q-select {
  font-size: 1.3rem;
}

.scroll {
  .q-item {
    font-size: 1.1rem;
  }
}

.q-tab__label {
  font-size: 1.1rem;
}

.q-header {
  background-color: #3460f2;
}

.ball {
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  padding: 0;
  margin-right: 0.5rem;
  align-items: center;
  justify-content: center;
  border: 1px solid #121212;
  border-radius: 50%;
  background-color: #feec37;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  font-size: 1.8rem;
  font-weight: bold;
}

.special-number {
  background-color: #ff5722;
  color: #f5f5f5;
}

.tail-number {
  background-color: #d0f0c0;
  color: #121212;
}

.appearance {
  .ball {
    width: 2.8rem;
    height: 2.8rem;
    font-size: 1.5rem;
  }
}

.predict {
  background-color: #c62828;
  color: #f5f5f5;
  font-weight: bolder;

  .ball {
    color: #121212;
  }
}

.predict-special-number .ball {
  color: #e53935;
}

.q-dialog {
  .q-card {
    max-width: 100%;
    width: 800px;
  }
}

.custom-selected-item {
  font-size: 0.85rem;
}

.sticky-scroll-table {
  /* bg color is important for th; just specify one */
  .q-table__top,
  .q-table__bottom,
  thead tr:first-child th {
    background-color: primary;
  }

  thead tr th {
    position: sticky;
    z-index: 1;
  }

  thead tr:first-child th {
    top: 0;
  }

  /* this is when the loading indicator appears */
  &.q-table--loading thead tr:last-child th {
    /* height of all previous header rows */
    top: 48px;
  }

  /* prevent scrolling behind sticky top row on focus */
  tbody {
    /* height of all previous header rows */
    scroll-margin-top: 48px;
  }
}

@media (max-width: 1199px) {
  .ball {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
  }
}

@media (max-width: 991px) {
  .ball {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .ball {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.3rem;
  }
}
