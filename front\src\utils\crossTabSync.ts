/**
 * 跨分頁同步工具類
 * 用於測試和調試跨分頁 token 同步功能
 */

export class CrossTabSyncTester {
  private static instance: CrossTabSyncTester;
  private listeners: Map<string, Function[]> = new Map();

  private constructor() {
    this.setupStorageListener();
  }

  public static getInstance(): CrossTabSyncTester {
    if (!CrossTabSyncTester.instance) {
      CrossTabSyncTester.instance = new CrossTabSyncTester();
    }
    return CrossTabSyncTester.instance;
  }

  private setupStorageListener() {
    window.addEventListener('storage', (event) => {
      console.log('Storage event detected:', {
        key: event.key,
        oldValue: event.oldValue,
        newValue: event.newValue,
        url: event.url,
        timestamp: new Date().toISOString()
      });

      // 觸發相應的監聽器
      const listeners = this.listeners.get(event.key || '');
      if (listeners) {
        listeners.forEach(listener => {
          try {
            listener(event);
          } catch (error) {
            console.error('Error in storage listener:', error);
          }
        });
      }
    });
  }

  /**
   * 添加存儲事件監聽器
   */
  public addListener(key: string, callback: Function) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    this.listeners.get(key)!.push(callback);
  }

  /**
   * 移除存儲事件監聽器
   */
  public removeListener(key: string, callback: Function) {
    const listeners = this.listeners.get(key);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 測試 token 同步
   */
  public testTokenSync() {
    console.log('Testing token sync...');
    
    const testToken = {
      access_token: 'test-access-token-' + Date.now(),
      expires_at: new Date(Date.now() + 3600000),
      refresh_token: 'test-refresh-token-' + Date.now(),
      refresh_expires_at: new Date(Date.now() + 7200000),
      timestamp: Date.now()
    };

    localStorage.setItem('auth-token-sync', JSON.stringify(testToken));
    
    setTimeout(() => {
      localStorage.removeItem('auth-token-sync');
      console.log('Test token sync completed');
    }, 100);
  }

  /**
   * 測試登出同步
   */
  public testLogoutSync() {
    console.log('Testing logout sync...');
    
    localStorage.setItem('auth-logout-sync', 'true');
    
    setTimeout(() => {
      localStorage.removeItem('auth-logout-sync');
      console.log('Test logout sync completed');
    }, 100);
  }

  /**
   * 獲取當前所有監聽器的狀態
   */
  public getListenersStatus() {
    const status: Record<string, number> = {};
    this.listeners.forEach((listeners, key) => {
      status[key] = listeners.length;
    });
    return status;
  }
}

// 導出單例實例
export const crossTabSyncTester = CrossTabSyncTester.getInstance();

// 在開發環境下將測試器添加到全局對象
if (process.env.NODE_ENV === 'development') {
  (window as any).crossTabSyncTester = crossTabSyncTester;
}
