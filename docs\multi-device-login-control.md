# 多設備登入控制功能

## 功能概述

本系統實現了完整的多設備登入控制功能，包括：

1. **設備數量限制**: 每個用戶最多只能在2台設備上登入
2. **強制登出通知**: 當新設備登入時，會強制登出其他設備並發送通知
3. **設備管理**: 管理員可以查看和管理用戶的設備
4. **登入歷史**: 管理員可以查看用戶的登入歷史記錄

## 主要特性

### 1. 設備數量限制
- 每個用戶最多允許2台設備同時登入
- 當第3台設備嘗試登入時，系統會拒絕登入並顯示錯誤訊息
- 錯誤訊息：「已達到最大設備數量限制(2台)，無法透過此裝置登入」

### 2. 強制登出機制
- 當用戶在新設備登入時，會自動登出所有其他設備
- 使用WebSocket實時通知被登出的設備
- 被登出的設備會收到通知：「您的帳號已在另一個設備登入」
- 自動跳轉到登入頁面

### 3. 設備識別
- 基於User-Agent和IP地址生成唯一的設備ID
- 智能識別設備名稱（包括手機型號、操作系統、瀏覽器等）
- 記錄設備的活動狀態和最後活動時間

### 4. 管理員設備管理
- 在用戶管理頁面添加設備管理按鈕
- 查看用戶當前註冊的設備列表
- 查看用戶登入歷史記錄（最近50次）
- 可以刪除用戶的特定設備
- 刪除設備時會自動通知該設備登出

## 技術實現

### 後端實現

#### 1. 設備模型 (UserDevice)
```go
type UserDevice struct {
    ID         int64     `gorm:"primaryKey" json:"id"`
    UserID     uint64    `json:"user_id"`
    DeviceID   string    `json:"device_id"`    // 設備唯一標識
    DeviceName string    `json:"device_name"`  // 設備名稱
    UserAgent  string    `json:"user_agent"`   // 用戶代理
    IsActive   bool      `json:"is_active"`    // 當前是否活躍
    LastSeenAt time.Time `json:"last_seen_at"` // 最後活動時間
    CreatedAt  time.Time `json:"created_at"`
    UpdatedAt  time.Time `json:"updated_at"`
    DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}
```

#### 2. 登入邏輯修改
- 檢查設備數量限制
- 生成設備ID並檢查是否已註冊
- 將所有設備設為非活躍狀態
- 吊銷所有有效token
- 註冊或更新當前設備
- 通知其他設備登出

#### 3. WebSocket通知
- 維護用戶設備的WebSocket連接映射
- 支持向特定設備發送登出通知
- 自動清理失效的連接

#### 4. 管理員API
- `GET /admin/users/:id/devices` - 獲取用戶設備列表
- `GET /admin/users/:id/login-history` - 獲取登入歷史
- `DELETE /admin/users/:id/devices/:device_id` - 刪除用戶設備

### 前端實現

#### 1. WebSocket處理增強
- 自動建立WebSocket連接
- 處理強制登出通知
- 支持連接重試機制
- 在PWA和網頁環境下都能正常工作

#### 2. 設備管理界面
- 用戶設備列表顯示
- 設備狀態指示器
- 登入歷史記錄表格
- 設備刪除功能

#### 3. 錯誤處理
- 設備數量限制錯誤提示
- 網絡連接錯誤處理
- 用戶友好的錯誤訊息

## 測試指南

### 1. 設備數量限制測試
1. 使用同一個帳號在第一台設備登入
2. 使用同一個帳號在第二台設備登入
3. 嘗試在第三台設備登入，應該被拒絕

### 2. 強制登出測試
1. 在設備A登入
2. 在設備B使用同一帳號登入
3. 設備A應該收到登出通知並跳轉到登入頁面

### 3. 管理員設備管理測試
1. 以管理員身份登入
2. 進入用戶管理頁面
3. 點擊用戶的設備管理按鈕
4. 查看設備列表和登入歷史
5. 嘗試刪除設備

### 4. PWA環境測試
1. 在PWA模式下進行上述所有測試
2. 確保WebSocket連接正常工作
3. 確保通知功能正常

## 配置說明

### 環境變數
- `VITE_WS_URL`: WebSocket連接URL（前端）
- 默認值：`ws://localhost:8080/api/ws`

### 設備數量限制
- 當前設置為2台設備
- 可在 `api/controllers/auth.go` 中修改限制數量

## 注意事項

1. **設備識別**: 基於User-Agent和IP地址，清除瀏覽器數據可能會被識別為新設備
2. **WebSocket連接**: 需要確保WebSocket URL配置正確
3. **跨域設置**: 確保WebSocket連接允許跨域請求
4. **數據庫**: 確保UserDevice表已正確創建
5. **Token管理**: 設備刪除時會自動吊銷相關token

## 故障排除

### WebSocket連接失敗
- 檢查WebSocket URL配置
- 確認後端WebSocket服務正常運行
- 檢查防火牆和代理設置

### 設備識別問題
- 檢查User-Agent和IP地址獲取
- 確認設備ID生成邏輯
- 查看設備名稱推斷結果

### 通知不工作
- 確認WebSocket連接狀態
- 檢查消息發送邏輯
- 查看瀏覽器控制台錯誤

## 未來改進

1. **設備管理增強**: 支持設備重命名、設備類型圖標等
2. **安全性提升**: 添加設備指紋識別、異常登入檢測等
3. **用戶體驗**: 支持用戶自主管理設備、設備信任機制等
4. **監控告警**: 添加異常登入行為監控和告警機制
