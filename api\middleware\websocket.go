package middleware

import (
	"crypto/md5"
	"encoding/hex"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	. "lottery/database"
	. "lottery/models"
)

// WebSocket認證中間件
func WebSocketAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 從查詢參數獲取token
		token := c.Query("token")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token is missing"})
			c.Abort()
			return
		}

		db := ConnectDB()
		defer CloseDB(db)

		s := NewTokenService(db)

		// 驗證token
		claims, _, err := s.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			c.Abort()
			return
		}

		// 生成設備ID
		userAgent := c.Request.UserAgent()
		deviceID := generateDeviceID(userAgent, c.ClientIP())

		// 將用戶ID和設備ID設置到context中
		c.Set("user_id", strconv.FormatUint(claims.UserID, 10))
		c.Set("device_id", deviceID)
		c.Set("claims", claims)

		c.Next()
	}
}

// 生成設備ID
func generateDeviceID(userAgent, clientIP string) string {
	// 使用 User-Agent 和 IP 地址生成設備唯一標識
	data := userAgent + clientIP
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

// 推斷設備名稱
func inferDeviceName(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	// 移動設備型號映射
	mobileModels := map[string]string{
		"iphone":         "iPhone",
		"ipad":           "iPad",
		"samsung":        "Samsung",
		"huawei":         "Huawei",
		"xiaomi":         "Xiaomi",
		"oppo":           "OPPO",
		"vivo":           "Vivo",
		"oneplus":        "OnePlus",
		"pixel":          "Google Pixel",
		"lg":             "LG",
		"sony":           "Sony",
		"htc":            "HTC",
		"motorola":       "Motorola",
		"nokia":          "Nokia",
		"blackberry":     "BlackBerry",
		"honor":          "Honor",
		"realme":         "Realme",
		"redmi":          "Redmi",
		"mi ":            "Xiaomi",
		"galaxy":         "Samsung Galaxy",
		"note":           "Samsung Note",
		"tab":            "Samsung Tab",
		"nexus":          "Google Nexus",
		"surface":        "Microsoft Surface",
	}

	// 操作系統映射
	osMap := map[string]string{
		"windows nt 10.0": "Windows 10",
		"windows nt 6.3":  "Windows 8.1",
		"windows nt 6.2":  "Windows 8",
		"windows nt 6.1":  "Windows 7",
		"windows nt 6.0":  "Windows Vista",
		"windows nt 5.1":  "Windows XP",
		"mac os x":        "macOS",
		"android":         "Android",
		"iphone os":       "iOS",
		"ipad":            "iPadOS",
		"linux":           "Linux",
		"ubuntu":          "Ubuntu",
		"fedora":          "Fedora",
		"cros":            "Chrome OS",
	}

	// 瀏覽器映射
	browserMap := map[string]string{
		"chrome":         "Chrome",
		"firefox":        "Firefox",
		"safari":         "Safari",
		"opera":          "Opera",
		"msie":           "Internet Explorer",
		"trident":        "Internet Explorer",
		"edge":           "Edge",
		"edg":            "Edge",
		"seamonkey":      "SeaMonkey",
		"ucbrowser":      "UC Browser",
		"qqbrowser":      "QQ Browser",
		"micromessenger": "WeChat",
		"wechat":         "WeChat",
	}

	// 檢測操作系統
	var osName string
	for key, name := range osMap {
		if strings.Contains(userAgent, key) {
			osName = name
			break
		}
	}

	// 檢測瀏覽器
	var browserName string
	for key, name := range browserMap {
		if strings.Contains(userAgent, key) {
			browserName = name
			break
		}
	}

	// 推斷移動設備型號（僅當是移動設備時）
	var modelName string
	if strings.Contains(userAgent, "mobile") ||
		strings.Contains(userAgent, "android") ||
		strings.Contains(userAgent, "iphone") {
		for key, name := range mobileModels {
			if strings.Contains(userAgent, key) {
				modelName = name
				break
			}
		}
	}

	// 從 User-Agent 提取更具體的設備信息
	deviceInfo := extractDeviceInfo(userAgent)

	// 組合設備名稱
	var deviceParts []string

	// 添加移動設備型號（如果有）
	if modelName != "" {
		if deviceInfo != "" {
			deviceParts = append(deviceParts, modelName+" "+deviceInfo)
		} else {
			deviceParts = append(deviceParts, modelName)
		}
	}

	// 添加操作系統（如果有）
	if osName != "" {
		deviceParts = append(deviceParts, osName)
	}

	// 添加瀏覽器（如果有）
	if browserName != "" {
		deviceParts = append(deviceParts, browserName)
	}

	// 如果沒有識別到任何信息，返回默認名稱
	if len(deviceParts) == 0 {
		return "未知設備"
	}

	return strings.Join(deviceParts, " - ")
}

// 從 User-Agent 提取設備信息
func extractDeviceInfo(userAgent string) string {
	// 這裡可以添加更複雜的邏輯來提取設備型號等信息
	// 目前返回空字符串，可以根據需要擴展
	return ""
}
